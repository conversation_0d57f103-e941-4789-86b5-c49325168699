"""added tax and return migrations

Revision ID: 83e435d95bb1
Revises: 6c5329119ac5
Create Date: 2025-08-13 07:42:19.320169

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '83e435d95bb1'
down_revision: Union[str, Sequence[str], None] = '6c5329119ac5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('order_items', sa.Column('cgst', sa.DECIMAL(precision=10, scale=2), nullable=False))
    op.add_column('order_items', sa.Column('sgst', sa.DECIMAL(precision=10, scale=2), nullable=False))
    op.add_column('order_items', sa.Column('igst', sa.DECIMAL(precision=10, scale=2), nullable=False))
    op.add_column('order_items', sa.Column('cess', sa.DECIMAL(precision=10, scale=2), nullable=False))
    op.add_column('order_items', sa.Column('is_returnable', sa.Boolean(), nullable=False))
    op.add_column('order_items', sa.Column('return_type', sa.String(length=2), nullable=False))
    op.add_column('order_items', sa.Column('return_window', sa.Integer(), nullable=False))
    op.add_column('order_items', sa.Column('selling_price_net', sa.DECIMAL(precision=10, scale=2), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('order_items', 'selling_price_net')
    op.drop_column('order_items', 'return_window')
    op.drop_column('order_items', 'return_type')
    op.drop_column('order_items', 'is_returnable')
    op.drop_column('order_items', 'cess')
    op.drop_column('order_items', 'igst')
    op.drop_column('order_items', 'sgst')
    op.drop_column('order_items', 'cgst')
    # ### end Alembic commands ###