"""create returns table

Revision ID: e5fc96c062ec
Revises: 8a4827761227
Create Date: 2025-08-24 05:36:08.359536

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e5fc96c062ec'
down_revision: Union[str, Sequence[str], None] = '8a4827761227'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('returns',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('return_reference', sa.String(length=50), nullable=False),
        sa.Column('order_id', sa.Integer(), nullable=False),
        sa.Column('customer_id', sa.String(length=50), nullable=False),
        sa.Column('return_type', sa.String(length=10), nullable=False),
        sa.Column('return_reason', sa.Text(), nullable=True),
        sa.Column('return_method', sa.String(length=20), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('total_refund_amount', sa.DECIMAL(precision=10, scale=2), nullable=True),
        sa.Column('refund_status', sa.String(length=20), nullable=False),
        sa.Column('approved_at', sa.TIMESTAMP(), nullable=True),
        sa.Column('processed_at', sa.TIMESTAMP(), nullable=True),
        sa.Column('completed_at', sa.TIMESTAMP(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(), nullable=False),
        sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_returns_created_at', 'returns', ['created_at'], unique=False)
    op.create_index('idx_returns_customer_status', 'returns', ['customer_id', 'status'], unique=False)
    op.create_index(op.f('ix_returns_customer_id'), 'returns', ['customer_id'], unique=False)
    op.create_index(op.f('ix_returns_id'), 'returns', ['id'], unique=False)
    op.create_index(op.f('ix_returns_order_id'), 'returns', ['order_id'], unique=False)
    op.create_index(op.f('ix_returns_return_reference'), 'returns', ['return_reference'], unique=True)
    op.create_index(op.f('ix_returns_status'), 'returns', ['status'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_returns_status'), table_name='returns')
    op.drop_index(op.f('ix_returns_return_reference'), table_name='returns')
    op.drop_index(op.f('ix_returns_order_id'), table_name='returns')
    op.drop_index(op.f('ix_returns_id'), table_name='returns')
    op.drop_index(op.f('ix_returns_customer_id'), table_name='returns')
    op.drop_index('idx_returns_customer_status', table_name='returns')
    op.drop_index('idx_returns_created_at', table_name='returns')
    op.drop_table('returns')
    # ### end Alembic commands ###
