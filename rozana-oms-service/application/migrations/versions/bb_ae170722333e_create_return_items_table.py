"""create return_items table

Revision ID: ae170722333e
Revises: e5fc96c062ec
Create Date: 2025-08-24 05:37:46.833598

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ae170722333e'
down_revision: Union[str, Sequence[str], None] = 'e5fc96c062ec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('return_items',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('return_id', sa.Integer(), nullable=False),
        sa.Column('order_item_id', sa.Integer(), nullable=False),
        sa.Column('sku', sa.String(length=100), nullable=False),
        sa.Column('quantity_returned', sa.Integer(), nullable=False),
        sa.Column('unit_price', sa.DECIMAL(precision=10, scale=2), nullable=False),
        sa.Column('sale_price', sa.DECIMAL(precision=10, scale=2), nullable=False),
        sa.Column('refund_amount', sa.DECIMAL(precision=10, scale=2), nullable=True),
        sa.Column('return_reason', sa.Text(), nullable=True),
        sa.Column('item_condition', sa.String(length=20), nullable=True),
        sa.Column('condition_notes', sa.Text(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['order_item_id'], ['order_items.id'], ),
        sa.ForeignKeyConstraint(['return_id'], ['returns.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_return_items_id'), 'return_items', ['id'], unique=False)
    op.create_index(op.f('ix_return_items_order_item_id'), 'return_items', ['order_item_id'], unique=False)
    op.create_index(op.f('ix_return_items_return_id'), 'return_items', ['return_id'], unique=False)
    op.create_index(op.f('ix_return_items_sku'), 'return_items', ['sku'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_return_items_sku'), table_name='return_items')
    op.drop_index(op.f('ix_return_items_return_id'), table_name='return_items')
    op.drop_index(op.f('ix_return_items_order_item_id'), table_name='return_items')
    op.drop_index(op.f('ix_return_items_id'), table_name='return_items')
    op.drop_table('return_items')
    # ### end Alembic commands ###
