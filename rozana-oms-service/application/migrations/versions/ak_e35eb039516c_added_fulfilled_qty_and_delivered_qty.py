"""added fulfilled qty and delivered qty

Revision ID: e35eb039516c
Revises: 77cabf7d34ce
Create Date: 2025-08-19 10:14:41.892732

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e35eb039516c'
down_revision: Union[str, Sequence[str], None] = '77cabf7d34ce'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('order_items', sa.Column('fulfilled_quantity', sa.Integer(), server_default='0', nullable=True))
    op.add_column('order_items', sa.Column('delivered_quantity', sa.Integer(), server_default='0', nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('order_items', 'delivered_quantity')
    op.drop_column('order_items', 'fulfilled_quantity')
    # ### end Alembic commands ###
