"""create return images table

Revision ID: c7620a7b2534
Revises: ae170722333e
Create Date: 2025-08-24 05:38:33.727246

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c7620a7b2534'
down_revision: Union[str, Sequence[str], None] = 'ae170722333e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('return_images',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('return_id', sa.Integer(), nullable=False),
        sa.Column('return_item_id', sa.Integer(), nullable=True),
        sa.Column('image', sa.String(length=500), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['return_id'], ['returns.id'], ),
        sa.ForeignKeyConstraint(['return_item_id'], ['return_items.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_return_images_id'), 'return_images', ['id'], unique=False)
    op.create_index(op.f('ix_return_images_return_id'), 'return_images', ['return_id'], unique=False)
    op.create_index(op.f('ix_return_images_return_item_id'), 'return_images', ['return_item_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_return_images_return_item_id'), table_name='return_images')
    op.drop_index(op.f('ix_return_images_return_id'), table_name='return_images')
    op.drop_index(op.f('ix_return_images_id'), table_name='return_images')
    op.drop_table('return_images')
    # ### end Alembic commands ###
