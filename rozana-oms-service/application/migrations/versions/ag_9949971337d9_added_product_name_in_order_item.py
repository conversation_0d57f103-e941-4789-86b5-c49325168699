"""added product name in order item

Revision ID: 9949971337d9
Revises: 83e435d95bb1
Create Date: 2025-08-16 11:26:06.563520

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9949971337d9'
down_revision: Union[str, Sequence[str], None] = '83e435d95bb1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('order_items', sa.Column('name', sa.String(length=200), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('order_items', 'name')
    # ### end Alembic commands ###
