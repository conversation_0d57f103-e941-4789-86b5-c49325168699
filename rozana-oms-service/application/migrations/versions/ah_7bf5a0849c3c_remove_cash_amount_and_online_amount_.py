"""Remove cash_amount and online_amount columns

Revision ID: 7bf5a0849c3c
Revises: 9949971337d9
Create Date: 2025-08-17 11:48:17.298402

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7bf5a0849c3c'
down_revision: Union[str, Sequence[str], None] = '9949971337d9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('payment_details', 'cash_amount')
    op.drop_column('payment_details', 'online_amount')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('payment_details', sa.Column('online_amount', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=False))
    op.add_column('payment_details', sa.Column('cash_amount', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
