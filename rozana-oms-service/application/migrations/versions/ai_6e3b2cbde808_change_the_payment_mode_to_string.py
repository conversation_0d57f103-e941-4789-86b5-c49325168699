"""change the payment mode to string

Revision ID: 6e3b2cbde808
Revises: 7bf5a0849c3c
Create Date: 2025-08-18 18:33:07.455940

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '6e3b2cbde808'
down_revision: Union[str, Sequence[str], None] = '7bf5a0849c3c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('payment_details', 'payment_mode',
               existing_type=postgresql.ENUM('cash', 'online', 'cash_and_online', name='payment_mode_enum'),
               type_=sa.String(length=50),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('payment_details', 'payment_mode',
               existing_type=sa.String(length=50),
               type_=postgresql.ENUM('cash', 'online', 'cash_and_online', name='payment_mode_enum'),
               existing_nullable=False)
    # ### end Alembic commands ###
