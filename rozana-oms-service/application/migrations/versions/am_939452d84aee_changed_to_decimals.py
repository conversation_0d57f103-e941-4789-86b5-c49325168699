"""changed to decimals

Revision ID: 939452d84aee
Revises: b5cfbf7e306b
Create Date: 2025-08-19 17:37:06.297989

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '939452d84aee'
down_revision: Union[str, Sequence[str], None] = 'b5cfbf7e306b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('order_items', 'quantity',
               existing_type=sa.INTEGER(),
               type_=sa.DECIMAL(precision=10, scale=2),
               existing_nullable=False)
    op.alter_column('order_items', 'fulfilled_quantity',
               existing_type=sa.INTEGER(),
               type_=sa.DECIMAL(precision=10, scale=2),
               existing_nullable=True,
               existing_server_default=sa.text('0'))
    op.alter_column('order_items', 'delivered_quantity',
               existing_type=sa.INTEGER(),
               type_=sa.DECIMAL(precision=10, scale=2),
               existing_nullable=True,
               existing_server_default=sa.text('0'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('order_items', 'delivered_quantity',
               existing_type=sa.DECIMAL(precision=10, scale=2),
               type_=sa.INTEGER(),
               existing_nullable=True,
               existing_server_default=sa.text('0'))
    op.alter_column('order_items', 'fulfilled_quantity',
               existing_type=sa.DECIMAL(precision=10, scale=2),
               type_=sa.INTEGER(),
               existing_nullable=True,
               existing_server_default=sa.text('0'))
    op.alter_column('order_items', 'quantity',
               existing_type=sa.DECIMAL(precision=10, scale=2),
               type_=sa.INTEGER(),
               existing_nullable=False)
    # ### end Alembic commands ###
