"""
Default payment behavior configuration.

Defines the initial payment status per payment mode to ensure
consistent creation of payment records across the system.
"""
from app.core.constants import PaymentStatus


class PaymentDefaults:
    """Centralized defaults for payment-related behavior."""

    # Initial status mapping per payment mode
    INITIAL_STATUS_BY_MODE = {
        # Cash paid at counter — mark as completed when record is created
        "cash": PaymentStatus.COMPLETED,
        # Cash-on-delivery — treated as pending until fulfillment
        "cod": PaymentStatus.PENDING,
        # Online modes — pending until verified
        "razorpay": PaymentStatus.PENDING,
        "wallet": PaymentStatus.PENDING,
    }

    @classmethod
    def initial_status_for_mode(cls, mode: str) -> int:
        """Return initial status for a given payment mode.

        Defaults to PENDING for unknown modes.
        """
        if not mode:
            return PaymentStatus.PENDING
        return cls.INITIAL_STATUS_BY_MODE.get(mode.lower(), PaymentStatus.PENDING)
