import os
import logging
from fastapi import Request, HTTPException, BackgroundTasks
from app.connections.database import get_db, get_read_db
from app.services.order_service import OrderService
from app.services.order_query_service import OrderQueryService

from app.dto.orders import OrderCreate, OrderResponse  # type: ignore
from app.validations.orders import OrderCreateValidator
from app.validations.payment_validations import PaymentValidator
from app.validations.stock import StockValidator
from app.validations.typesense import TypesenseValidator
from app.core.constants import OrderStatus, PaymentStatus
from app.core.payment_defaults import PaymentDefaults
from app.integrations.potions_service import potions_service
from app.services.order_service import OrderService
from app.services.wallet_payment_service import wallet_payment_service

STOCK_CHECK_ENABLED = os.getenv("STOCK_CHECK_ENABLED", "false").lower() == "true"

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger("app.core.order_functions")


async def create_order_core(order: OrderCreate, request: Request, background_tasks: BackgroundTasks, origin: str = "app"):
  
    try:
        user_id = getattr(request.state, "user_id", None)
        logger.info("Create order requested by user_id=%s, origin=%s", user_id, origin)

        # Validation Layer
        validator = OrderCreateValidator(order, user_id)
        validator.validate_items_count()
        validator.validate_duplicate_sku_items()
        
        # Payment Validation Layer
        payment_validator = PaymentValidator(order)
        payment_validator.validate_payment_configuration(origin)

        # Typesense Validation Layer
        typesense_validator = TypesenseValidator()
        products, validation_errors = await typesense_validator.validate_items(
            order.items, 
            order.facility_name, 
            order_amount=order.total_amount
        )
        if validation_errors:
            raise HTTPException(status_code=400, detail=validation_errors)

        # Typesense Enrichment Layer add more details as in tax and pack_uom_qty and wh_sku details for each item
        enriched_items, enrich_errors = await typesense_validator.enrich_items(order.items, products, order.facility_name)
        if enrich_errors:
            raise HTTPException(status_code=400, detail=enrich_errors)

        # Only validate the IMS stock check for app origin
        if origin == "app":
            validator.validate_user_id_customer_id()
            if STOCK_CHECK_ENABLED:
                for item in enriched_items:
                    stock_validator = StockValidator(order.facility_name, item["wh_sku"])
                    stock_validator.validate_stock(item["quantity"] * item["pack_uom_quantity"])

        # Service Layer
        service = OrderService()
        order_data = order.model_dump()
        order_data['items'] = enriched_items
        result = await service.create_order(order_data, origin)


        if not result.get("success", False):
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to create order"))

        #Block the stock in redis by reducing the available quantity
        if STOCK_CHECK_ENABLED and origin == "app":
            # now loop through the enriched_items and block quantity * pack_uom_qty
            for item in enriched_items:
                stock_validator = StockValidator(order.facility_name, item["wh_sku"])
                stock_validator.block_stock(item["quantity"] * item["pack_uom_quantity"])

        # Create payment records in database
        payment_modes = [p.payment_mode.lower() for p in order.payment]
        payment_records = await create_payment_records_for_order(
            order_id=result["id"],
            order_external_id=result["order_id"],
            payments=order.payment,
            total_amount=order.total_amount,
            order_data=order_data,
            background_tasks=background_tasks
        )

        is_cod_order = all(pm in ["cash", "cod"] for pm in payment_modes)
        has_wallet_payment = "wallet" in payment_modes
        
        if background_tasks and result.get("order_id"):
            if is_cod_order:
                # For COD/cash orders, sync to WMS immediately
                background_tasks.add_task(
                    potions_service.sync_order_by_id,
                    order.facility_name,
                    result["order_id"],
                    service
                )
                logger.info(f"Scheduled immediate WMS sync for COD/CASH order {result['order_id']}")
            elif has_wallet_payment:
                # For wallet payments, schedule background job to confirm and update order status
                background_tasks.add_task(
                    wallet_payment_service.confirm_wallet_payments_and_update_order,
                    result["id"]  # Use internal order ID
                )
                logger.info(f"Scheduled wallet confirmation job for order {result['order_id']}")
            else:
                # For online/razorpay payment orders, WMS sync will be triggered after payment completion
                logger.info(f"Skipping immediate WMS sync for order {result['order_id']} - will sync after payment completion")

        # Add the payment details
        result["payment_order_details"] = payment_records
        return result
    except HTTPException:
        raise
    except ValueError as exc:
        raise HTTPException(status_code=400, detail=str(exc)) from exc
    except Exception as exc:
        import traceback
        logger.error("Error creating order: %s", str(traceback.print_exc()))
        raise HTTPException(status_code=500, detail="Internal server error") from exc


async def get_order_details_core(order_id: str):
    """Shared logic for fetching full order details."""
    try:
        service = OrderQueryService()
        order = service.get_order_by_id(order_id)
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")
        return order
    except HTTPException:
        raise
    except Exception as exc:  # pylint: disable=broad-except
        logger.error("Error getting order details %s: %s", order_id, exc)
        raise HTTPException(status_code=500, detail="Internal server error") from exc


async def get_all_orders_core(request: Request, limit: int, offset: int, sort_order: str):
    """Shared logic for listing all orders for the authenticated user."""
    user_id = getattr(request.state, "user_id", None)
    try:
        service = OrderQueryService()
        # Validate page parameters
        validator = OrderCreateValidator(user_id=user_id)
        validator.validate_page_size(limit, offset)

        orders = service.get_all_orders(user_id, limit, offset, sort_order)
        if not orders:
            raise HTTPException(status_code=404, detail="No orders found")
        return orders
    except HTTPException:
        raise
    except Exception as exc:  # pylint: disable=broad-except
        logger.error("Error getting all orders for user %s: %s", user_id, exc)
        raise HTTPException(status_code=500, detail="Internal server error") from exc

async def get_all_facility_orders_core(request: Request, facility_name: str, limit: int, offset: int, sort_order: str):
    """Shared logic for listing all orders for a facility."""
    try:
        service = OrderQueryService()
        # Validate page parameters
        validator = OrderCreateValidator()
        validator.validate_page_size(limit, offset)

        orders = service.get_all_facility_orders(facility_name, limit, offset, sort_order)
        if not orders:
            raise HTTPException(status_code=404, detail="No orders found")
        return orders
    except HTTPException:
        raise
    except Exception as exc:  # pylint: disable=broad-except
        logger.error("Error getting all orders for facility %s: %s", facility_name, exc)
        raise HTTPException(status_code=500, detail="Internal server error") from exc


async def create_payment_order_for_existing_order(
    order_id: str,
    customer_id: str,
    customer_name: str,
    customer_email: str = None,
    customer_phone: str = None,
    amount: float = None
):
    """Create a Razorpay payment order for an existing OMS order using existing service."""
    from app.integrations.razorpay_service import razorpay_service
    
    try:
        # Get the existing order to extract amount if not provided
        order = await get_order_by_id(order_id)
        if not order:
            raise HTTPException(status_code=404, detail=f"Order {order_id} not found")
        
        # Use provided amount or fall back to order's total_amount
        payment_amount = amount or order.get("total_amount")
        if not payment_amount:
            raise ValueError("Payment amount not available")
        
        # Prepare customer details with defaults
        customer_details = {
            "customer_id": customer_id,
            "customer_name": customer_name,
            "customer_email": customer_email or f"{customer_id}@gmail.com",
            "customer_phone": customer_phone or "9999999999"
        }

        # Reuse existing Razorpay service method
        result = await razorpay_service.create_razorpay_order(
            order_id=order_id,
            amount=payment_amount,
            customer_details=customer_details,
            notes={
                "oms_order_id": order_id,
                "customer_id": customer_id,
                "customer_name": customer_name
            }
        )
        
        if result["success"]:
            return {
                "razorpay_order_id": result["razorpay_order_id"],
                "amount": result["amount"],
                "amount_paise": result["amount_paise"],
                "currency": result["currency"],
                "key_id": result["key_id"],
                "customer_details": customer_details,
                "created_at": result["created_at"]
            }
        else:
            if result.get("skipped"):
                raise ValueError("Razorpay integration is disabled")
            else:
                raise ValueError(result.get("message", "Failed to create payment order"))
        
    except Exception as exc:
        logger.error("Error creating payment order for %s: %s", order_id, exc)
        raise exc


async def get_order_by_id(order_id: str):
    """Get order details by order ID for payment operations."""
    try:
        service = OrderQueryService()
        result = service.get_order_by_id(order_id)
        
        if not result:
            return None
            
        return result
        
    except Exception as e:
        logger.error(f"Error fetching order {order_id}: {e}")
        return None


async def create_payment_records_for_order(
    order_id: int,
    order_external_id: str,
    payments: list,
    total_amount: float,
    order_data: dict,
    background_tasks = None
):
    """Create payment records for an order (handles both single and split payments)."""
    try:
        from app.services.payment_service import payment_service
        from decimal import Decimal
        import uuid
        
        payment_records = []
        payment_count = len(payments)
        
        logger.info(f"Creating {payment_count} payment record(s) for order {order_external_id}")
        
        for payment in payments:
            try:
                # Map payment modes to database enum values and generate mode-specific payment ID
                db_payment_mode = payment.payment_mode.lower()
                if db_payment_mode == 'razorpay':
                    db_payment_mode = 'razorpay'  # Use actual database enum value
                    payment_id = f"RAZOR_{uuid.uuid4().hex[:8].upper()}"
                elif db_payment_mode == 'cod':
                    db_payment_mode = 'cod'       # Use actual database enum value
                    payment_id = f"COD_{uuid.uuid4().hex[:8].upper()}"
                elif db_payment_mode == 'cash':
                    db_payment_mode = 'cash'      # Use actual database enum value
                    payment_id = f"CASH_{uuid.uuid4().hex[:8].upper()}"
                elif db_payment_mode == 'wallet':
                    db_payment_mode = 'wallet'    # Use actual database enum value
                    payment_id = f"WALLET_{uuid.uuid4().hex[:8].upper()}"
                else:
                    # Fallback for unknown payment modes
                    payment_id = f"PAY_{uuid.uuid4().hex[:8].upper()}"

                # Handle wallet payment processing
                if db_payment_mode == 'wallet':
                    # Process wallet payment as background job
                    payment_id = f"WALLET_{uuid.uuid4().hex[:8].upper()}"
                    
                    # Get customer ID from order data or request state
                    customer_id = order_data.get('customer_id')
                    if not customer_id:
                        logger.error(f"Customer ID not found for wallet payment in order {order_external_id}")
                        continue
                    
                    # Schedule wallet payment processing as background job
                    

                # create the razorpay order
                payment_order_id = ""
                if db_payment_mode == 'razorpay':
                    payment_order_details = await create_payment_order_for_existing_order(
                        order_id=order_external_id,
                        customer_id=order_data['customer_id'],
                        customer_name=order_data['customer_name'],
                        customer_email=order_data.get('customer_email', ""),
                        customer_phone=order_data.get('customer_phone', ""),
                        amount=Decimal(str(payment.amount))
                    )
                    payment_order_id = payment_order_details.get("razorpay_order_id")

                # Create payment record
                initial_status = PaymentDefaults.initial_status_for_mode(db_payment_mode)
                payment_result = await payment_service.create_payment_record(
                    order_id=order_id,
                    payment_id=payment_id,
                    payment_amount=Decimal(str(payment.amount)),
                    payment_mode=db_payment_mode,
                    payment_status=initial_status,
                    total_amount=Decimal(str(total_amount)),
                    payment_order_id=payment_order_id
                )

                if payment_result.get("success"):
                    payment_records.append(payment_result)
                    logger.info(f"Created payment record for {payment.payment_mode}: {payment.amount}")
                else:
                    logger.error(f"Failed to create payment record for {payment.payment_mode}: {payment_result.get('message')}")
                
                if db_payment_mode == 'wallet':
                    # Only attempt wallet debit when explicitly requested AND wallet is the sole payment mode
                    if getattr(payment, 'create_payment_order', False) and payment_count == 1:
                        # Process wallet payment synchronously so response reflects final status (51/52)
                        from app.services.wallet_payment_service import wallet_payment_service
                        wallet_process_result = await wallet_payment_service.process_wallet_payment(
                            customer_id=customer_id,
                            order_id=order_id,
                            amount=Decimal(str(payment.amount)),
                            payment_id=payment_id,
                            description=f"Payment for order {order_external_id}"
                        )
                        # Refresh and update the returned payment status in response
                        updated_payment = await payment_service.get_payment_by_id(payment_id)
                        if updated_payment:
                            payment_result["status"] = updated_payment.payment_status
                            logger.info(
                                f"Wallet payment {payment_id} processed synchronously: {wallet_process_result.get('status')} -> status_code={updated_payment.payment_status}"
                            )
                    else:
                        # Do not attempt debit; keep initial status 50 (PENDING)
                        reason = (
                            "create_payment_order not set for wallet"
                            if not getattr(payment, 'create_payment_order', False) else
                            "split payment order; wallet will remain PENDING"
                        )
                        logger.info(
                            f"Skipping wallet debit for order {order_external_id}: {reason}"
                        )
            except Exception as payment_exc:
                logger.error(f"Error creating payment record for {payment.payment_mode}: {payment_exc}")
        
        return payment_records
        
    except Exception as e:
        logger.error(f"Error creating payment records for order {order_external_id}: {e}")
        return []


async def create_payment_for_order(
    order_id: str,
    payment_id: str,
    payment_amount: float,
    payment_mode: str = "online"
):
    """Create a payment record for an order (payments table only)."""
    try:
        from app.services.payment_service import payment_service
        from decimal import Decimal
        
        # First, get the internal integer ID from the external string order_id
        order = await get_order_by_id(order_id)
        if not order:
            logger.error(f"Order not found for order_id: {order_id}")
            return {"success": False, "message": f"Order {order_id} not found"}
        
        internal_order_id = order.get("id")  # Get the internal integer ID
        
        payment_modes = [p.payment_mode.lower() for p in order.payment]
        
        initial_status = PaymentDefaults.initial_status_for_mode(payment_mode)
        result = await payment_service.create_payment_record(
            order_id=internal_order_id,  # Use internal integer ID
            payment_id=payment_id,
            payment_amount=Decimal(str(payment_amount)),
            payment_mode=payment_mode,
            payment_status=initial_status,
            total_amount=Decimal(str(order.get("total_amount", 0)))  # Pass the total order amount
        )
        
        if result.get("success"):
            logger.info(f"Created payment record for order {order_id}, payment {payment_id}")
        else:
            logger.error(f"Failed to create payment record for order {order_id}: {result.get('message')}")
            
        return result
        
    except Exception as e:
        logger.error(f"Error creating payment for order {order_id}: {e}")
        return {"success": False, "message": str(e)}


async def get_payment_status_for_order(order_id: str):
    """Get payment status summary for an order (from payments table only)."""
    try:
        from app.services.payment_service import payment_service

        # First, get the internal integer ID from the external string order_id
        order = await get_order_by_id(order_id)
        if not order:
            logger.error(f"Order not found for order_id: {order_id}")
            return {"success": False, "message": f"Order {order_id} not found"}

        internal_order_id = order.get("id")  # Get the internal integer ID

        result = await payment_service.get_payment_status_for_order(internal_order_id)

        logger.info(f"Retrieved payment status for order {order_id}")
        return result

    except Exception as e:
        logger.error(f"Error getting payment status for order {order_id}: {e}")
        return {
            "order_id": order_id,
            "has_payments": False,
            "payment_status": None,
            "total_paid": 0.0,
            "payment_count": 0,
            "error": str(e)
        }


async def update_existing_payment(
    order_id: str,
    razorpay_payment_id: str,
    payment_amount: float,
    payment_status: int
):
    """Update existing pending payment record with Razorpay details instead of creating duplicate."""
    try:
        from app.services.payment_service import payment_service
        
        # Get the internal integer ID from the external string order_id
        order = await get_order_by_id(order_id)
        if not order:
            logger.error(f"Order not found for order_id: {order_id}")
            return {"success": False, "message": f"Order {order_id} not found"}
        
        internal_order_id = order.get("id")
        
        # Find the existing pending payment record for this order
        result = await payment_service.update_pending_payment_with_razorpay_details(
            order_id=internal_order_id,
            razorpay_payment_id=razorpay_payment_id,
            payment_amount=payment_amount,
            payment_status=payment_status
        )
        
        if result.get("success"):
            logger.info(f"Updated existing payment record for order {order_id} with Razorpay payment {razorpay_payment_id}")
        else:
            logger.error(f"Failed to update existing payment record for order {order_id}: {result.get('message')}")
            
        return result
        
    except Exception as e:
        logger.error(f"Error updating existing payment for order {order_id}: {e}")
        return {"success": False, "message": str(e)}

