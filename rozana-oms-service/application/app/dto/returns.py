from pydantic import BaseModel, Field, condecimal, field_validator, model_validator
from typing import List, Optional, Dict

# ---- Consolidated DTOs used by app routes and cores ----

class OrderReturnItemRequest(BaseModel):
    sku: str = Field(..., min_length=1, max_length=100, description="SKU of the item to return")
    quantity: condecimal(max_digits=12, decimal_places=2, gt=0) = Field(..., description="Quantity to return (must be integer-equivalent, e.g., 1 or 1.0)")

    @field_validator("quantity")
    def quantity_must_be_integer(cls, v):
        # v is Decimal due to condecimal; allow 1, 2, 5.00 (integer-equivalent),
        # but reject 1.5, 0.25, etc.
        if v != v.to_integral_value():
            raise ValueError("quantity must be an integer (no fractional part)")
        return v


class CreateReturnRequest(BaseModel):
    order_id: str = Field(..., min_length=1, max_length=50, description="Order ID for return")
    items: Optional[List[OrderReturnItemRequest]] = Field(None, description="Items to return (if provided, treated as partial return)")
    order_full_return: Optional[bool] = Field(False, description="When true and items not provided, process full return")
    return_reason: Optional[str] = Field(None, max_length=1000, description="Reason for return")

    @model_validator(mode="after")
    def validate_items_or_full_return(self):
        has_items = bool(self.items) and len(self.items) > 0
        has_full = bool(self.order_full_return)
        if not has_items and not has_full:
            raise ValueError("Provide non-empty items or set order_full_return=true")
        return self


class OrderReturnResponse(BaseModel):
    success: bool
    message: str
    order_id: str
    return_reference: Optional[str] = Field(None, description="Generated return reference")
    return_type: str = Field(..., description="Type of return: 'partial' or 'full'")
    returned_items: List[Dict] = Field(default_factory=list, description="List of returned items with details")
    total_refund_amount: float = Field(0.0, description="Total refund amount")
    order_status: int
    wms_status: Optional[str] = None
