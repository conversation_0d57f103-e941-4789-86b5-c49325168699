from pydantic import BaseModel, Field, field_validator
from typing import List, Dict, Optional
from datetime import datetime
import re

class OrderItemCreate(BaseModel):
    sku: str = Field(..., min_length=1, max_length=100)
    name: Optional[str] = Field(None, max_length=200, description="Optional item name")
    quantity: int = Field(..., gt=0)
    unit_price: float = Field(..., gte=0)
    sale_price: float = Field(..., gte=0)


class PaymentInfo(BaseModel):
    payment_mode: str = Field(..., description="Payment mode: 'cash', 'razorpay', 'cod'")
    create_payment_order: bool = Field(False, description="Create payment order for online payments")
    amount: Optional[float] = Field(None, description="Amount for this payment mode")

    @field_validator("payment_mode")
    def validate_payment_mode(cls, payment_mode):
        # Allow all basic payment modes - origin-specific validation happens in service layer
        allowed = {"cash", "razorpay", "cod", "wallet"}
        if payment_mode.lower() not in allowed:
            raise ValueError(f"Invalid payment_mode: {payment_mode}")
        return payment_mode.lower()

    @field_validator("create_payment_order")
    def validate_create_payment_order(cls, v, info):
        """Validate create_payment_order based on payment mode"""
        payment_mode = info.data.get("payment_mode", "").lower()
        
        if payment_mode == "razorpay" and not v:
            raise ValueError("create_payment_order must be true for razorpay ")
        
        if payment_mode == "cash" and v:
            raise ValueError("create_payment_order must be false for cash")
        
        if payment_mode == "wallet" and not v:
            raise ValueError("create_payment_order must be true for wallet")
        
        return v


class OrderAddress(BaseModel):
    full_name: str
    phone_number: str
    address_line1: str
    address_line2: Optional[str] = None
    city: str
    state: str
    postal_code: str
    country: str = "india"
    type_of_address: str
    longitude: Optional[float] = Field(None, ge=-180, le=180, description="Longitude coordinate (between -180 and 180)")
    latitude: Optional[float] = Field(None, ge=-90, le=90, description="Latitude coordinate (between -90 and 90)")

    @field_validator("type_of_address")
    def validate_type(cls, v):
        allowed = {"work", "home", "other"}
        if v not in allowed:
            raise ValueError("type_of_address must be one of: 'work', 'home', 'other'")
        return v

    @field_validator("phone_number")
    def validate_phone(cls, v):
        pattern = r"^(\+\d{1,2}\s?)?1?\-?\s?\(?\d{3}\)?[\s\-]?\d{3}[\s\-]?\d{4}$"
        if not re.fullmatch(pattern, v):
            raise ValueError("Invalid phone number format")
        return v

    @field_validator("longitude")
    def validate_longitude(cls, v):
        if v is not None and (v < -180 or v > 180):
            raise ValueError("Longitude must be between -180 and 180")
        return v

    @field_validator("latitude")
    def validate_latitude(cls, v):
        if v is not None and (v < -90 or v > 90):
            raise ValueError("Latitude must be between -90 and 90")
        return v

class OrderCreate(BaseModel):
    customer_id: str = Field(..., min_length=1, max_length=50)
    customer_name: str = Field(..., min_length=1, max_length=100)
    facility_id: str = Field(..., min_length=1, max_length=50)
    facility_name: str = Field(..., min_length=1, max_length=100)
    status: str = Field("pending", pattern="^(pending|confirmed|delivered|cancelled)$")
    total_amount: float = Field(..., gt=0)
    is_approved: bool = Field(True)
    items: List[OrderItemCreate]
    address: OrderAddress
    payment: List[PaymentInfo] = Field(..., min_items=1, description="List of payment information for multiple payment modes")    
    # Payment integration fields (backward compatibility)
    customer_email: Optional[str] = Field(None, description="Customer email for payment order")
    customer_phone: Optional[str] = Field(None, description="Customer phone for payment order")

class OrderStatusUpdate(BaseModel):
    order_id: str = Field(..., min_length=1, max_length=50)
    status: str = Field(..., pattern="^(pending|confirmed|delivered|cancelled)$")

class OrderItemStatusUpdate(BaseModel):
    order_id: str = Field(..., min_length=1, max_length=50)
    sku: str = Field(..., min_length=1, max_length=100)
    status: str = Field(..., pattern="^(pending|confirmed|delivered|cancelled|refunded)$")

class OrderCancelRequest(BaseModel):
    order_id: str = Field(..., min_length=1, max_length=50, description="Order ID to cancel")

class OrderCancelResponse(BaseModel):
    success: bool
    message: str
    order_id: str
    status: str
    wms_status: Optional[str] = None

## Return-related DTOs moved to app/dto/returns.py

class OrderResponse(BaseModel):
    success: bool
    message: str
    order_id: str
    eta: Optional[str] = None  # Changed from datetime to str for readable format
    
    # Payment order details (when auto_create_payment_order=True)
    payment_order_details: Optional[List[Dict]] = Field(None, description="List of Razorpay payment order details")