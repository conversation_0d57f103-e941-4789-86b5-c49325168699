"""
SQLAlchemy ORM Models
These models define the database schema using SQLAlchemy ORM.
Use these for internal/small queries that benefit from ORM features.
"""

from sqlalchemy import Column, Integer, String, DECIMAL, TIMESTAMP, ForeignKey, Computed, Index, Enum, Boolean,Text
from sqlalchemy.orm import relationship
from app.models.common import CommonModel

class Order(CommonModel):
    """
    Order model using SQLAlchemy ORM.
    Maps to the orders table created in the migration.
    """
    __tablename__ = "orders"

    id = Column(Integer, primary_key=True, index=True)
    random_prefix = Column(String(4), nullable=False)
    order_id = Column(String(50), Computed("random_prefix || id::TEXT", persisted=True), unique=True, nullable=False, index=True)
    customer_id = Column(String(50), nullable=False, index=True)
    customer_name = Column(String(100), nullable=False)
    facility_id = Column(String(50), nullable=False, index=True)
    facility_name = Column(String(100), nullable=False)
    status = Column(Integer, nullable=False, default=10, index=True)
    total_amount = Column(DECIMAL(10, 2), nullable=False)
    eta = Column(TIMESTAMP, nullable=True)
    order_mode = Column(String(20), Enum('web', 'app', 'pos', name='order_mode_enum'), nullable=False)
    is_approved = Column(Boolean, nullable=False, default=False)
    handling_charge = Column(DECIMAL(10, 2), nullable=False, default=0.00, server_default="0.00")
    surge_charge = Column(DECIMAL(10, 2), nullable=False, default=0.00, server_default="0.00")
    delivery_charge = Column(DECIMAL(10, 2), nullable=False, default=0.00, server_default="0.00")

    def __repr__(self):
        return f"<Order(id={self.id}, order_id='{self.order_id}', customer_id='{self.customer_id}', status={self.status})>"

    # Composite indexes for better query performance
    __table_args__ = (
        Index('idx_orders_customer_status', 'customer_id', 'status'),
        Index('idx_orders_facility_status', 'facility_id', 'status'),
        Index('idx_orders_status_created', 'status', 'created_at'),
    )



# Additional models can be added here as the schema grows
# For example:

class OrderItem(CommonModel):
    """
    Order items model - for future use when order items table is added
    """
    __tablename__ = "order_items"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False, index=True)
    sku = Column(String(100), nullable=False, index=True)
    name = Column(String(200), nullable=True)  # Optional name field
    quantity = Column(DECIMAL(10, 2), nullable=False)
    fulfilled_quantity = Column(DECIMAL(10, 2), default=0, server_default="0")
    delivered_quantity = Column(DECIMAL(10, 2), default=0, server_default="0")
    unit_price = Column(DECIMAL(10, 2), nullable=False)
    sale_price = Column(DECIMAL(10, 2), nullable=False)
    status = Column(Integer, nullable=False, default=10)
    pack_uom_quantity = Column(Integer, nullable=False, default=1)
    wh_sku = Column(String(255))
    thumbnail_url = Column(Text, nullable=True)

    # Tax fields
    cgst = Column(DECIMAL(10, 2), nullable=False, default=0.00)
    sgst = Column(DECIMAL(10, 2), nullable=False, default=0.00)
    igst = Column(DECIMAL(10, 2), nullable=False, default=0.00)
    cess = Column(DECIMAL(10, 2), nullable=False, default=0.00)

    # Return policy fields
    is_returnable = Column(Boolean, nullable=False, default=False)
    return_type = Column(String(2), nullable=False, default='00')
    return_window = Column(Integer, nullable=False, default=0)

    # Selling price net field
    selling_price_net = Column(DECIMAL(10, 2), nullable=False, default=0.00)

    # Relationship back to order
    order = relationship("Order", backref="items")

    def __repr__(self):
        return f"<OrderItem(id={self.id}, order_id='{self.order_id}', sku='{self.sku}', quantity={self.quantity})>"

    # Composite indexes for better query performance
    __table_args__ = (
        Index('idx_order_items_order_sku', 'order_id', 'sku'),
        Index('idx_order_items_sku_status', 'sku', 'status'),
        Index('idx_order_items_order_status', 'order_id', 'status'),
    )



class OrderAddress(CommonModel):
    """
    Order address model - for future use when order addresses table is added
    """
    __tablename__ = "order_addresses"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False, index=True)
    full_name = Column(String(200), nullable=False)
    phone_number = Column(String(20), nullable=False)
    address_line1 = Column(String(255), nullable=False)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=False)
    state = Column(String(100), nullable=False)
    postal_code = Column(String(20), nullable=False)
    country = Column(String(100), nullable=False, default="india")
    type_of_address = Column(String(20), nullable=False)
    longitude = Column(DECIMAL(10, 7), nullable=True)
    latitude = Column(DECIMAL(10, 7), nullable=True)

    # Relationship back to order
    order = relationship("Order", backref="addresses")

    def __repr__(self):
        return f"<OrderAddress(id={self.id}, order_id='{self.order_id}', type='{self.type_of_address}')>"

    # Composite indexes for better query performance
    __table_args__ = (
        Index('idx_order_addresses_order_type', 'order_id', 'type_of_address'),
        Index('idx_order_addresses_city_state', 'city', 'state'),
        Index('idx_order_addresses_postal_country', 'postal_code', 'country'),
        Index('idx_order_addresses_coordinates', 'longitude', 'latitude'),
    )


