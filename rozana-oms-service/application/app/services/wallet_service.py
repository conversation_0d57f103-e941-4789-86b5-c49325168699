"""
External Wallet Service for handling wallet operations.

This service communicates with an external wallet API to handle
wallet balance checks, debit operations, and transaction confirmations.
"""

import os
import logging
import httpx
from typing import Dict, Any, Optional
from decimal import Decimal
import uuid
from datetime import datetime

logger = logging.getLogger(__name__)

class WalletService:
    """Service for communicating with external wallet API"""
    
    def __init__(self):
        self.wallet_enabled = os.getenv("WALLET_INTEGRATION_ENABLED", "false").lower() == "true"
        self.wallet_api_url = os.getenv("WALLET_BASE_URL", "")
        self.wallet_api_key = os.getenv("WALLET_INTERNAL_API_KEY", "")
        self.timeout = 30  # seconds
        
        if not self.wallet_enabled:
            logger.info("Wallet integration is disabled")
        elif not self.wallet_api_url:
            logger.warning("WALLET_BASE_URL not configured")
        elif not self.wallet_api_key:
            logger.warning("WALLET_INTERNAL_API_KEY not configured")
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers for wallet API requests"""
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.wallet_api_key}",
            "User-Agent": "Rozana-OMS/1.0"
        }
    
    
    async def check_balance(self, customer_id: str) -> Dict[str, Any]:
        """
        Check wallet balance for a customer.
        
        Args:
            customer_id: Customer ID
            
        Returns:
            Dict with balance information
        """
        try:
            if not self.wallet_enabled:
                return {
                    "success": False,
                    "message": "Wallet service disabled",
                    "balance": 0.0
                }
            if not self.wallet_api_url or not self.wallet_api_key:
                return {
                    "success": False,
                    "message": "Wallet service not configured",
                    "balance": 0.0
                }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.wallet_api_url}/balance/{customer_id}",
                    headers=self._get_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "success": True,
                        "balance": float(data.get("balance", 0.0)),
                        "currency": data.get("currency", "INR"),
                        "is_active": data.get("is_active", True)
                    }
                elif response.status_code == 404:
                    return {
                        "success": False,
                        "message": "Wallet not found",
                        "balance": 0.0
                    }
                else:
                    logger.error(f"Balance check failed: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "message": f"Balance check failed: {response.status_code}",
                        "balance": 0.0
                    }
                    
        except httpx.TimeoutException:
            logger.error("Wallet service timeout during balance check")
            return {
                "success": False,
                "message": "Wallet service timeout",
                "balance": 0.0
            }
        except Exception as e:
            logger.error(f"Error checking wallet balance: {e}")
            return {
                "success": False,
                "message": f"Balance check error: {str(e)}",
                "balance": 0.0
            }
    
    async def add_wallet_entry(
        self, 
        customer_id: str, 
        amount: Decimal, 
        order_id: str,
        payment_id: str,
        entry_type: str = "debit",
        reference_type: str = "order_payment",
        description: str = None
    ) -> Dict[str, Any]:
        """
        Add wallet entry (debit/credit) using the actual wallet API format.
        
        Args:
            customer_id: Customer ID
            amount: Amount for the entry
            order_id: Order ID for reference
            entry_type: "debit" or "credit"
            reference_type: Type of reference (e.g., "order_payment")
            description: Optional description
            
        Returns:
            Dict with wallet entry result
        """
        try:
            if not self.wallet_enabled:
                return {
                    "success": False,
                    "message": "Wallet service disabled",
                    "transaction_id": None
                }
            
            if not self.wallet_api_url or not self.wallet_api_key:
                return {
                    "success": False,
                    "message": "Wallet service not configured",
                    "transaction_id": None
                }
            
            related_id = f"oms_{payment_id}_{uuid.uuid4().hex[:8]}"
            
            payload = {
                "related_id": related_id,
                "wallet_amt": float(amount),
                "entry_type": entry_type,
                "description": description or f"Payment for order {order_id}",
                "reference_type": reference_type
            }
            
            # Use the actual API endpoint format
            url = f"{self.wallet_api_url}/internal/wallet-entry/{customer_id}/add-entry"
            headers = {
                "Content-Type": "application/json",
                "x-api-key": self.wallet_api_key
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    url,
                    headers=headers,
                    json=payload
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "success": True,
                        "transaction_id": related_id,
                        "amount": float(amount),
                        "balance_after": data.get("balance_after", 0.0),
                        "status": data.get("status", "completed"),
                        "message": "Wallet entry added successfully"
                    }
                elif response.status_code == 400:
                    data = response.json()
                    return {
                        "success": False,
                        "message": data.get("message", "Insufficient balance"),
                        "transaction_id": None
                    }
                else:
                    logger.error(f"Wallet entry failed: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "message": f"Wallet entry failed: {response.status_code}",
                        "transaction_id": None
                    }
                    
        except httpx.TimeoutException:
            logger.error("Wallet service timeout during wallet entry")
            return {
                "success": False,
                "message": "Wallet service timeout",
                "transaction_id": None
            }
        except Exception as e:
            logger.error(f"Error adding wallet entry: {e}")
            return {
                "success": False,
                "message": f"Wallet entry error: {str(e)}",
                "transaction_id": None
            }
    
    async def confirm_transaction(self, transaction_id: str) -> Dict[str, Any]:
        """
        Confirm a held wallet transaction.
        
        Args:
            transaction_id: Transaction ID to confirm
            
        Returns:
            Dict with confirmation result
        """
        try:
            if not self.wallet_api_url or not self.wallet_api_key:
                return {
                    "success": False,
                    "message": "Wallet service not configured"
                }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.wallet_api_url}/confirm/{transaction_id}",
                    headers=self._get_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "success": True,
                        "transaction_id": transaction_id,
                        "status": data.get("status", "confirmed"),
                        "message": "Transaction confirmed successfully"
                    }
                elif response.status_code == 404:
                    return {
                        "success": False,
                        "message": "Transaction not found"
                    }
                else:
                    logger.error(f"Transaction confirmation failed: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "message": f"Confirmation failed: {response.status_code}"
                    }
                    
        except httpx.TimeoutException:
            logger.error("Wallet service timeout during confirmation")
            return {
                "success": False,
                "message": "Wallet service timeout"
            }
        except Exception as e:
            logger.error(f"Error confirming transaction: {e}")
            return {
                "success": False,
                "message": f"Confirmation error: {str(e)}"
            }
    
    async def cancel_transaction(self, transaction_id: str) -> Dict[str, Any]:
        """
        Cancel a held wallet transaction (release the hold).
        
        Args:
            transaction_id: Transaction ID to cancel
            
        Returns:
            Dict with cancellation result
        """
        try:
            if not self.wallet_api_url or not self.wallet_api_key:
                return {
                    "success": False,
                    "message": "Wallet service not configured"
                }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.wallet_api_url}/cancel/{transaction_id}",
                    headers=self._get_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "success": True,
                        "transaction_id": transaction_id,
                        "status": data.get("status", "cancelled"),
                        "message": "Transaction cancelled successfully"
                    }
                elif response.status_code == 404:
                    return {
                        "success": False,
                        "message": "Transaction not found"
                    }
                else:
                    logger.error(f"Transaction cancellation failed: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "message": f"Cancellation failed: {response.status_code}"
                    }
                    
        except httpx.TimeoutException:
            logger.error("Wallet service timeout during cancellation")
            return {
                "success": False,
                "message": "Wallet service timeout"
            }
        except Exception as e:
            logger.error(f"Error cancelling transaction: {e}")
            return {
                "success": False,
                "message": f"Cancellation error: {str(e)}"
            }


# Global wallet service instance
wallet_service = WalletService()

# Global wallet service instance
wallet_service = WalletService()
